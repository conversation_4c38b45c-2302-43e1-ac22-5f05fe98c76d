<?php
namespace app\api\controller;

use think\Controller;
use think\Cache;
//use GatewayClient\Gateway;

class BaseController extends Controller{
	//初始化方法
	protected function initialize(){	
		
	 	parent::initialize();

		header("Access-Control-Allow-Origin:*");
		header("Access-Control-Allow-Methods:GET, POST, OPTIONS, DELETE");
		header("Access-Control-Allow-Headers:DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type, Accept-Language, Origin, Accept-Encoding");	
		//清除所有缓存
		//app('cache')->clear();
		
		//内存缓存 内存
		ini_set ('memory_limit', '800M');
		//接口白名单		
		
		//不用用户登录可以访问的页面
		$no_user_id_arr	= array('code','login','getdownloadurl','checkslogin','sendsmscode','register','checksmsresetpw','resetpassword','backdata','createorder','orderlist','orderrecordlist','repayment','signin','gettaskranklist','gettaskclasslist','getvipuserexpire','gettasklist','gettaskinfo','taskordertrial','getlanguage','test','unifiedcallback','unifiedwithdrawalcallback','rechargecallback','getpublickey','public_key','testdecryptlog','testpostdecrypt','testgetdecrypt','testputdecrypt','testdeletedecrypt','testallrequesttypes','testfrontenddata','establishsession');

		$action = request()->action();

		// 调试：记录action名称
		\think\facade\Log::info('BaseController action: ' . $action);
		\think\facade\Log::info('BaseController controller: ' . request()->controller());
		\think\facade\Log::info('BaseController module: ' . request()->module());

		//获取提交用户提交数据 并保存
		$param = input('param.') ?: [];

		// 统一处理所有HTTP请求方式的加密数据（避免重复解密）
		$this->handleAllRequestTypesDecryption();

		// 重新获取可能已解密的param数据
		$param = input('param.') ?: [];

		//更新vip会员
		/*$UserVipData	= model('UserVip')->where(array(['state','=',1],['etime','<',strtotime(date("Y-m-d",time()))]))->select()->toArray();
		
		foreach($UserVipData as $key=>$value){
			//会员过期
			$isup =  model('UserVip')->where('id' , $value['id'])->update(array('state'=>3));
			//更新会员等级
			if($isup){
				model('Users')->where('id', $value['uid'])->update(array('vip_level'=>1));//普通会员
			}
		}*/
		
		// 重写input函数的行为，优先从解密后的数据获取
		$this->overrideInputBehavior();
		
		$lang = (input('post.lang')) ? input('post.lang') : 'id';	// 语言类型
		if(!in_array($action,$no_user_id_arr)){

			$user_token = input('post.token') ? input('post.token') : input('get.token');
			
			// 调试：检查token获取情况
			\think\facade\Log::info('🔍 Token获取调试:');
			\think\facade\Log::info('🔍 input(post.token): ' . (input('post.token') ?: 'NULL'));
			\think\facade\Log::info('🔍 input(get.token): ' . (input('get.token') ?: 'NULL'));
			\think\facade\Log::info('🔍 input(param.token): ' . (input('param.token') ?: 'NULL'));
			\think\facade\Log::info('🔍 $_POST[token]: ' . (isset($_POST['token']) ? $_POST['token'] : 'NULL'));
			\think\facade\Log::info('🔍 $_REQUEST[token]: ' . (isset($_REQUEST['token']) ? $_REQUEST['token'] : 'NULL'));
			\think\facade\Log::info('🔍 最终user_token: ' . ($user_token ?: 'NULL'));

			//检查 userid usertoken
			if(!$user_token){
				$data['code'] = 203;
				
			if($lang=='cn')	$data['code_dec'] = '没有登录';
			elseif($lang=='en') $data['code_dec'] 	= 'not logged on';
			elseif($lang=='id') $data['code_dec']	= 'tidak didaftar';
			elseif($lang=='ft') $data['code_dec']	= '沒有登錄';
			elseif($lang=='vi') $data['code_dec']	= 'chưa đăng nhập';
			elseif($lang=='es') $data['code_dec']	= 'Sin comentarios';
			elseif($lang=='ja') $data['code_dec']	= 'ログインしていません';
			elseif($lang=='th') $data['code_dec']	= 'ไม่มีล็อกอิน';
			elseif($lang=='yd') $data['code_dec']	= 'लगइन नहीं है';
			elseif($lang=='ma') $data['code_dec']	= 'tidak log masuk';
			elseif($lang=='pt') $data['code_dec']	= 'Não ligado';

				ajax_return($data);
			}
			
			$user_token		= stripslashes($user_token);
			
			$userArr 		= explode(',',auth_code($user_token,'DECODE'));//用户信息数组
			
			$uid 			= $userArr[0];//uid
			$username 		= isset($userArr[1]) ? $userArr[1] : '';//username

			//检查缓存是否存在
			if(!cache('C_token_'.$uid)){
				$data['code']		= 203;//没有登录
				
			if($lang=='cn')	$data['code_dec']	= '没有登录';
			elseif($lang=='en') $data['code_dec'] 	= 'not logged on';
			elseif($lang=='id') $data['code_dec']	= 'tidak didaftar';
			elseif($lang=='ft') $data['code_dec']	= '沒有登錄';
			elseif($lang=='vi') $data['code_dec']	= 'chưa đăng nhập';
			elseif($lang=='es') $data['code_dec']	= 'Sin comentarios';
			elseif($lang=='ja') $data['code_dec']	= 'ログインしていません';
			elseif($lang=='th') $data['code_dec']	= 'ไม่มีล็อกอิน';
			elseif($lang=='yd') $data['code_dec']	= 'लगइन नहीं है';
            elseif($lang=='ma') $data['code_dec']	= 'tidak log masuk';
            elseif($lang=='pt') $data['code_dec']	= 'Não ligado';
				ajax_return($data);
			}
			
			if(cache('C_token_'.$uid) != $user_token){
				$data['code']		= 204;//长时间没操作请重新登录
			if($lang=='cn') $data['code_dec']	= '长时间没操作请重新登录';
			elseif($lang=='en') $data['code_dec'] = 'No operation for a long time, please log in again!';
			elseif($lang=='id') $data['code_dec']	= 'Tidak ada operasi untuk waktu yang lama, silakan log masuk lagi';
			elseif($lang=='ft') $data['code_dec']	= '長時間沒操作請重新登入';
			elseif($lang=='vi') $data['code_dec']	= 'Không có hoạt động trong một thời gian dài, xin hãy đăng nhập lại lần nữa.';
			elseif($lang=='es') $data['code_dec']	= 'Por favor inicie de nuevo.';
			elseif($lang=='ja') $data['code_dec']	= '長い間操作していません。再登録してください。';
			elseif($lang=='th') $data['code_dec']	= 'กรุณาเข้าสู่ระบบอีกครั้งเมื่อคุณไม่ได้ทำงานเป็นเวลานาน';
			elseif($lang=='yd') $data['code_dec'] = 'लंबे समय के लिए कोई आपरेशन नहीं, कृपया फिर लॉग इन करें';
			elseif($lang=='ma') $data['code_dec'] = 'Tiada operasi untuk masa yang lama, sila log masuk lagi';
			elseif($lang=='pt') $data['code_dec'] = 'Sem operação por um Longo tempo, por favor, login novamente';
				ajax_return($data);
			}
			
			//检查用户是否存在
			$isuser = model('Users')->where(array('id'=>$uid,'username'=>$username,'state'=>1))->count();
			if(!$isuser){
				$data['code']		= 203;
				
			if($lang=='cn') $data['code_dec']	= '用户不存在';
			elseif($lang=='en') $data['code_dec'] = 'User does not exist!';
			elseif($lang=='id') $data['code_dec']	= 'pengguna tidak ada';
			elseif($lang=='ft') $data['code_dec']	= '用戶不存在';
			elseif($lang=='yd') $data['code_dec']	= 'उपयोक्ता मौजूद नहीं है';
			elseif($lang=='vi') $data['code_dec']	= 'người dùng không tồn tại';
			elseif($lang=='es') $data['code_dec']	= 'Usuario no existente';
			elseif($lang=='ja') $data['code_dec']	= 'ユーザが存在しません';
			elseif($lang=='th') $data['code_dec']	= 'ผู้ใช้ไม่มี';
			elseif($lang=='ma') $data['code_dec']	= 'pengguna tidak wujud';
			elseif($lang=='pt') $data['code_dec']	= 'O utilizador não existe';
				ajax_return($data);
			}
			
			//增加缓存的时间
			cache('C_token_'.$uid,$user_token,7200);

			//登录接口 添加提交数据日志
			$params = [];
			$values = [];
			if (is_array($param)) {
				foreach ($param as $key => $value) {
					$params[] = $key;
					$values[] = $value;
				}
			}

			$homelog = array(
				'uid'		=>	$uid,
				'time'		=>	time(),
				'params'	=>	json_encode($params),
				'values'	=>	json_encode($values),
				'ip'		=>	$this->request->ip(),
				'func'		=>	$this->request->action(),
				'cla'		=>	$this->request->controller()
			);
			model('Homelog')->insert($homelog);
		}
    }

	
	public function getDownloadUrl(){
    	$setting = model('Setting')->field('android,iphone')->where('id','>',0)->findOrEmpty();
    	if (!$setting) return json(['code'=>0,'code_dec'=>'无法获取下载地址！']);

		$data['android'] = $setting['android'];
		$data['iphone']  = $setting['iphone'];

    	return json(['code'=>1,'data'=>$data]);
    }

    /**
	 * 检查是否为加密请求
	 */
	protected function isEncryptedRequest($param)
	{
		if (!is_array($param)) {
			return false;
		}

		// 检查是否为会话级加密请求（session_id + data + iv）
		$isSessionEncrypted = isset($param['encrypted']) && $param['encrypted'] === 'true' &&
			   isset($param['session_id']) && isset($param['data']) && isset($param['iv']);

		if ($isSessionEncrypted) {
			\think\facade\Log::info('🔍 检测到会话级加密请求');
			return 'session';
		}

		// 检查是否为混合加密请求（需要RSA+AES解密）
		$isHybridEncrypted = isset($param['encrypted']) && $param['encrypted'] === 'true' &&
			   isset($param['key']) && isset($param['data']) &&
			   isset($param['iv']) && isset($param['salt_signature']);

		if ($isHybridEncrypted) {
			\think\facade\Log::info('🔍 检测到混合加密请求');
			return 'hybrid';
		}

		// 检查是否为简单加密请求（可能有其他加密字段）
		if (isset($param['encrypted']) && $param['encrypted'] === 'true') {
			\think\facade\Log::info('🔍 检测到简单加密请求');
			return 'simple';
		}

		return false;
	}

	/**
	 * 处理加密请求
	 */
	protected function handleEncryptedRequest($param)
	{
		try {
			// 检查加密类型
			$encryptType = $this->isEncryptedRequest($param);

			if ($encryptType === 'session') {
				// 会话级加密解密
				\think\facade\Log::info('处理会话级加密请求', [
					'session_id' => $param['session_id'],
					'data_length' => strlen($param['data']),
					'iv_length' => strlen($param['iv'])
				]);

				$decryptResult = \app\common\service\CryptoService::decryptWithSession(
					$param['data'],
					$param['iv'], 
					$param['session_id']
				);

				if (!$decryptResult['success']) {
					$errorMessage = isset($decryptResult['error_msg']) ? $decryptResult['error_msg'] : 
								   (isset($decryptResult['error']) ? $decryptResult['error'] : '会话解密失败');
					\think\facade\Log::error('会话级解密失败: ' . $errorMessage);
					// 解密失败时返回原始参数，让业务逻辑处理错误
					return $param;
				}

				\think\facade\Log::info('会话级解密成功', [
					'original_param_keys' => array_keys($param),
					'decrypted_data' => $decryptResult['data']
				]);

				// 将解密后的数据合并到参数中
				$decryptedData = $decryptResult['data'];
				if (is_array($decryptedData)) {
					// 如果解密后是数组，合并到参数中
					$param = array_merge($param, $decryptedData);
				} else {
					// 如果解密后是字符串，尝试JSON解析
					$jsonData = json_decode($decryptedData, true);
					if (json_last_error() === JSON_ERROR_NONE && is_array($jsonData)) {
						$param = array_merge($param, $jsonData);
					} else {
						$param['decrypted_data'] = $decryptedData;
					}
				}

				// 保留解密相关信息
				$param['_crypto_decrypted'] = true;
				$param['_crypto_session_id'] = $param['session_id'];

				return $param;

			} elseif ($encryptType === 'hybrid') {
				// 混合加密解密（原有逻辑）
				\think\facade\Log::info('处理混合加密请求');

				// 构造解密请求数据
				$requestData = [
					'key' => $param['key'],
					'data' => $param['data'],
					'iv' => $param['iv'],
					'salt_signature' => $param['salt_signature']
				];

				// 调用解密服务
				$decryptResult = \app\common\service\CryptoService::x1($requestData);

				if (!$decryptResult['success']) {
					$errorMessage = isset($decryptResult['message']) ? $decryptResult['message'] : 
								   (isset($decryptResult['error']) ? $decryptResult['error'] : '解密失败');
					\think\facade\Log::error('混合解密失败: ' . $errorMessage);
					// 解密失败时返回原始参数，让业务逻辑处理错误
					return $param;
				}

				\think\facade\Log::info('混合解密成功', [
					'original_param_keys' => array_keys($param),
					'decrypted_data' => $decryptResult['data']
				]);

				// 将解密后的数据合并到参数中
				$decryptedData = $decryptResult['data'];
				if (is_array($decryptedData)) {
					// 如果解密后是数组，合并到参数中
					$param = array_merge($param, $decryptedData);
				} else {
					// 如果解密后是字符串，尝试JSON解析
					$jsonData = json_decode($decryptedData, true);
					if (json_last_error() === JSON_ERROR_NONE && is_array($jsonData)) {
						$param = array_merge($param, $jsonData);
					} else {
						$param['decrypted_data'] = $decryptedData;
					}
				}

				// 保留解密相关信息
				$param['_crypto_decrypted'] = true;
				$param['_crypto_salt'] = $decryptResult['salt'];

				return $param;
			}

			return $param;

		} catch (\Exception $e) {
			\think\facade\Log::error('加密请求处理异常: ' . $e->getMessage());
			return $param;
		}
	}

	/**
	 * 处理所有HTTP请求方式的加密数据自动解密
	 */
	private function handleAllRequestTypesDecryption()
	{
		$requestMethod = request()->method();
		\think\facade\Log::info('处理HTTP请求加密数据', [
			'method' => $requestMethod,
			'content_type' => request()->header('content-type')
		]);

		// 检查是否已经处理过解密（避免重复解密）
		if (isset($_REQUEST['_crypto_processed'])) {
			\think\facade\Log::info('加密数据已处理，跳过重复解密');
			return;
		}

		// 寻找加密数据（按优先级检查各种参数来源）
		$encryptedData = $this->findEncryptedData();
		
		if ($encryptedData) {
			\think\facade\Log::info('检测到加密请求，开始一次性解密');
			// 只解密一次
			$decryptedResult = $this->handleEncryptedRequest($encryptedData);
			
			if (isset($decryptedResult['_crypto_decrypted'])) {
				// 将解密结果分发到各个参数容器
				$this->distributeDecryptedData($decryptedResult, $requestMethod);
				\think\facade\Log::info('解密数据已分发到各参数容器');
				
				// 打印解密后的具体数据内容 - 使用更详细的方式
				$cleanData = [];
				foreach ($decryptedResult as $key => $value) {
					if (!in_array($key, ['encrypted', 'key', 'data', 'iv', 'salt_signature', '_crypto_decrypted', '_crypto_salt'])) {
						$cleanData[$key] = $value;
					}
				}
				
				\think\facade\Log::info('🔍 解密后的数据字段数量: ' . count($cleanData));
				\think\facade\Log::info('🔍 解密后的数据字段名: ' . implode(', ', array_keys($cleanData)));
				
				// 逐个打印每个字段的值
				foreach ($cleanData as $key => $value) {
					\think\facade\Log::info("🔍 解密字段 [{$key}]: " . (is_array($value) ? json_encode($value, JSON_UNESCAPED_UNICODE) : $value));
				}
				
				// 也打印完整的解密结果（以防上面的过滤有问题）
				\think\facade\Log::info('🔍 完整解密结果内容: ' . json_encode($decryptedResult, JSON_UNESCAPED_UNICODE));
			}
		} else {
			\think\facade\Log::info('未检测到加密请求');
		}
	}

	/**
	 * 寻找加密数据
	 * @return array|null 找到的加密数据或null
	 */
	private function findEncryptedData()
	{
		// 按优先级检查各种参数来源
		$sources = [
			'param' => input('param.'),
			'post' => input('post.'),
			'get' => input('get.'),
		];
		
		foreach ($sources as $sourceType => $data) {
			if ($this->isEncryptedRequest($data)) {
				\think\facade\Log::info("在{$sourceType}参数中发现加密数据");
				return $data;
			}
		}
		
		// 检查其他HTTP方法的请求体数据
		$requestMethod = request()->method();
		switch (strtoupper($requestMethod)) {
			case 'PUT':
				$putData = $this->getPutRequestData();
				if ($this->isEncryptedRequest($putData)) {
					\think\facade\Log::info('在PUT请求体中发现加密数据');
					return $putData;
				}
				break;
			case 'DELETE':
				$deleteData = $this->getDeleteRequestData();
				if ($this->isEncryptedRequest($deleteData)) {
					\think\facade\Log::info('在DELETE请求体中发现加密数据');
					return $deleteData;
				}
				break;
			case 'PATCH':
				$patchData = $this->getPatchRequestData();
				if ($this->isEncryptedRequest($patchData)) {
					\think\facade\Log::info('在PATCH请求体中发现加密数据');
					return $patchData;
				}
				break;
		}
		
		return null;
	}

	/**
	 * 将解密后的数据分发到各个参数容器
	 * @param array $decryptedData 解密后的数据
	 * @param string $requestMethod 请求方法
	 */
	private function distributeDecryptedData($decryptedData, $requestMethod)
	{
		// 首先移除原始的加密字段
		$encryptedFields = ['encrypted', 'key', 'data', 'iv', 'salt_signature'];
		foreach ($encryptedFields as $field) {
			unset($_REQUEST[$field]);
			if (strtoupper($requestMethod) === 'POST') {
				unset($_POST[$field]);
			} elseif (strtoupper($requestMethod) === 'GET') {
				unset($_GET[$field]);
			}
		}
		
		\think\facade\Log::info('🔧 已移除原始加密字段: ' . implode(', ', $encryptedFields));
		
		// 设置解密后的业务数据到全局变量，让input()函数能够获取
		// 优先使用payload数据，如果没有就使用整个解密结果，但要过滤掉加密相关字段
		if (isset($decryptedData['payload']) && is_array($decryptedData['payload'])) {
			$businessData = $decryptedData['payload'];
		} else {
			// 过滤掉加密相关字段
			$businessData = [];
			foreach ($decryptedData as $key => $value) {
				if (!in_array($key, ['encrypted', 'key', 'data', 'iv', 'salt', 'salt_signature', '_crypto_decrypted', '_crypto_salt', '_crypto_processed'])) {
					$businessData[$key] = $value;
				}
			}
		}
		
		// 调试：打印解密后的数据结构
		\think\facade\Log::info('🔍 解密后完整数据: ' . json_encode($decryptedData, JSON_UNESCAPED_UNICODE));
		\think\facade\Log::info('🔍 业务数据: ' . json_encode($businessData, JSON_UNESCAPED_UNICODE));
		
		// 确保$businessData是数组
		if (is_array($businessData)) {
			// 获取当前Request对象
			$request = request();
			
			// 获取现有参数（原始数据，不经过过滤）
			$currentParams = $request->param(false) ?: [];
			
			// 合并解密后的业务数据
			foreach ($businessData as $key => $value) {
				$currentParams[$key] = $value;
				
				// 同时设置到全局变量
				$_REQUEST[$key] = $value;
				if (strtoupper($requestMethod) === 'POST') {
					$_POST[$key] = $value;
				} elseif (strtoupper($requestMethod) === 'GET') {
					$_GET[$key] = $value;
				}
				
				\think\facade\Log::info("🔍 设置参数: {$key} = {$value}");
			}
			
			// 使用反射重新设置Request对象的param属性
			$reflection = new \ReflectionClass($request);
			if ($reflection->hasProperty('param')) {
				$paramProperty = $reflection->getProperty('param');
				$paramProperty->setAccessible(true);
				$paramProperty->setValue($request, $currentParams);
				\think\facade\Log::info("✅ 成功更新Request对象参数");
			}
		}
		
		// 标记已处理，避免重复解密
		$_REQUEST['_crypto_processed'] = true;
		if (strtoupper($requestMethod) === 'POST') {
			$_POST['_crypto_processed'] = true;
		} elseif (strtoupper($requestMethod) === 'GET') {
			$_GET['_crypto_processed'] = true;
		}
		
		\think\facade\Log::info('🔧 解密数据分发完成，当前$_REQUEST keys: ' . implode(', ', array_keys($_REQUEST)));
	}



	/**
	 * 获取PUT请求的数据
	 */
	private function getPutRequestData()
	{
		$input = file_get_contents('php://input');
		if (empty($input)) {
			return [];
		}

		$contentType = request()->header('content-type');
		if (strpos($contentType, 'application/json') !== false) {
			$data = json_decode($input, true);
			return is_array($data) ? $data : [];
		} elseif (strpos($contentType, 'application/x-www-form-urlencoded') !== false) {
			parse_str($input, $data);
			return is_array($data) ? $data : [];
		}

		return [];
	}

	/**
	 * 设置PUT请求的数据
	 */
	private function setPutRequestData($data)
	{
		foreach ($data as $key => $value) {
			if (!in_array($key, ['encrypted', 'key', 'data', 'iv', 'salt_signature', '_crypto_decrypted', '_crypto_salt'])) {
				$_REQUEST[$key] = $value;
				// 同时设置到PUT参数中（如果ThinkPHP支持）
				if (isset($_PUT)) {
					$_PUT[$key] = $value;
				}
			}
		}
	}

	/**
	 * 获取DELETE请求的数据
	 */
	private function getDeleteRequestData()
	{
		$input = file_get_contents('php://input');
		if (empty($input)) {
			return [];
		}

		$contentType = request()->header('content-type');
		if (strpos($contentType, 'application/json') !== false) {
			$data = json_decode($input, true);
			return is_array($data) ? $data : [];
		} elseif (strpos($contentType, 'application/x-www-form-urlencoded') !== false) {
			parse_str($input, $data);
			return is_array($data) ? $data : [];
		}

		return [];
	}

	/**
	 * 设置DELETE请求的数据
	 */
	private function setDeleteRequestData($data)
	{
		foreach ($data as $key => $value) {
			if (!in_array($key, ['encrypted', 'key', 'data', 'iv', 'salt_signature', '_crypto_decrypted', '_crypto_salt'])) {
				$_REQUEST[$key] = $value;
			}
		}
	}

	/**
	 * 获取PATCH请求的数据
	 */
	private function getPatchRequestData()
	{
		$input = file_get_contents('php://input');
		if (empty($input)) {
			return [];
		}

		$contentType = request()->header('content-type');
		if (strpos($contentType, 'application/json') !== false) {
			$data = json_decode($input, true);
			return is_array($data) ? $data : [];
		} elseif (strpos($contentType, 'application/x-www-form-urlencoded') !== false) {
			parse_str($input, $data);
			return is_array($data) ? $data : [];
		}

		return [];
	}

	/**
	 * 设置PATCH请求的数据
	 */
	private function setPatchRequestData($data)
	{
		foreach ($data as $key => $value) {
			if (!in_array($key, ['encrypted', 'key', 'data', 'iv', 'salt_signature', '_crypto_decrypted', '_crypto_salt'])) {
				$_REQUEST[$key] = $value;
			}
		}
	}

	/**
	 * 重写input函数行为，让它能获取到解密后的参数
	 */
	private function overrideInputBehavior()
	{
		// 如果已经处理过加密数据，则重新设置Request的内部数据
		if (isset($_REQUEST['_crypto_processed'])) {
			$request = request();
			
			// 不清空缓存，而是重新设置为正确的数据
			$reflection = new \ReflectionClass($request);
			
			// 重新设置param属性为当前的$_REQUEST数据
			if ($reflection->hasProperty('param')) {
				$paramProperty = $reflection->getProperty('param');
				$paramProperty->setAccessible(true);
				$paramProperty->setValue($request, $_REQUEST);
			}
			
			// 重新设置post属性为当前的$_POST数据
			if ($reflection->hasProperty('post')) {
				$postProperty = $reflection->getProperty('post');
				$postProperty->setAccessible(true);
				$postProperty->setValue($request, $_POST);
			}
			
			// 重新设置get属性为当前的$_GET数据
			if ($reflection->hasProperty('get')) {
				$getProperty = $reflection->getProperty('get');
				$getProperty->setAccessible(true);
				$getProperty->setValue($request, $_GET);
			}
			
			\think\facade\Log::info("✅ 已重新设置Request对象数据");
		}
	}
    }

    /**
	 * 检查是否为加密请求
	 */
	protected function isEncryptedRequest($param)
	{
		if (!is_array($param)) {
			return false;
		}

		// 检查是否为混合加密请求（需要RSA+AES解密）
		$isHybridEncrypted = isset($param['encrypted']) && $param['encrypted'] === 'true' &&
			   isset($param['key']) && isset($param['data']) &&
			   isset($param['iv']) && isset($param['salt_signature']);

		if ($isHybridEncrypted) {
			\think\facade\Log::info('🔍 检测到混合加密请求');
			return 'hybrid';
		}

		// 检查是否为简单加密请求（可能有其他加密字段）
		if (isset($param['encrypted']) && $param['encrypted'] === 'true') {
			\think\facade\Log::info('🔍 检测到简单加密请求');
			return 'simple';
		}

		return false;
	}

	/**
	 * 处理加密请求
	 */
	protected function handleEncryptedRequest($param)
	{
		try {
			// 构造解密请求数据
			$requestData = [
				'key' => $param['key'],
				'data' => $param['data'],
				'iv' => $param['iv'],
				'salt_signature' => $param['salt_signature']
			];

			// 调用解密服务
			$decryptResult = \app\common\service\CryptoService::x1($requestData);

			if (!$decryptResult['success']) {
				$errorMessage = isset($decryptResult['message']) ? $decryptResult['message'] : 
							   (isset($decryptResult['error']) ? $decryptResult['error'] : '解密失败');
				\think\facade\Log::error('自动解密失败: ' . $errorMessage);
				// 解密失败时返回原始参数，让业务逻辑处理错误
				return $param;
			}

			\think\facade\Log::info('自动解密成功', [
				'original_param_keys' => array_keys($param),
				'decrypted_data' => $decryptResult['data']
			]);

			// 将解密后的数据合并到参数中
			$decryptedData = $decryptResult['data'];
			if (is_array($decryptedData)) {
				// 如果解密后是数组，合并到参数中
				$param = array_merge($param, $decryptedData);
			} else {
				// 如果解密后是字符串，尝试JSON解析
				$jsonData = json_decode($decryptedData, true);
				if (json_last_error() === JSON_ERROR_NONE && is_array($jsonData)) {
					$param = array_merge($param, $jsonData);
				} else {
					$param['decrypted_data'] = $decryptedData;
				}
			}

			// 保留解密相关信息
			$param['_crypto_decrypted'] = true;
			$param['_crypto_salt'] = $decryptResult['salt'];

			return $param;

		} catch (\Exception $e) {
			\think\facade\Log::error('加密请求处理异常: ' . $e->getMessage());
			return $param;
		}
	}

	/**
	 * 处理所有HTTP请求方式的加密数据自动解密
	 */
	private function handleAllRequestTypesDecryption()
	{
		$requestMethod = request()->method();
		\think\facade\Log::info('处理HTTP请求加密数据', [
			'method' => $requestMethod,
			'content_type' => request()->header('content-type')
		]);

		// 检查是否已经处理过解密（避免重复解密）
		if (isset($_REQUEST['_crypto_processed'])) {
			\think\facade\Log::info('加密数据已处理，跳过重复解密');
			return;
		}

		// 寻找加密数据（按优先级检查各种参数来源）
		$encryptedData = $this->findEncryptedData();
		
		if ($encryptedData) {
			\think\facade\Log::info('检测到加密请求，开始一次性解密');
			// 只解密一次
			$decryptedResult = $this->handleEncryptedRequest($encryptedData);
			
			if (isset($decryptedResult['_crypto_decrypted'])) {
				// 将解密结果分发到各个参数容器
				$this->distributeDecryptedData($decryptedResult, $requestMethod);
				\think\facade\Log::info('解密数据已分发到各参数容器');
				
				// 打印解密后的具体数据内容 - 使用更详细的方式
				$cleanData = [];
				foreach ($decryptedResult as $key => $value) {
					if (!in_array($key, ['encrypted', 'key', 'data', 'iv', 'salt_signature', '_crypto_decrypted', '_crypto_salt'])) {
						$cleanData[$key] = $value;
					}
				}
				
				\think\facade\Log::info('🔍 解密后的数据字段数量: ' . count($cleanData));
				\think\facade\Log::info('🔍 解密后的数据字段名: ' . implode(', ', array_keys($cleanData)));
				
				// 逐个打印每个字段的值
				foreach ($cleanData as $key => $value) {
					\think\facade\Log::info("🔍 解密字段 [{$key}]: " . (is_array($value) ? json_encode($value, JSON_UNESCAPED_UNICODE) : $value));
				}
				
				// 也打印完整的解密结果（以防上面的过滤有问题）
				\think\facade\Log::info('🔍 完整解密结果内容: ' . json_encode($decryptedResult, JSON_UNESCAPED_UNICODE));
			}
		} else {
			\think\facade\Log::info('未检测到加密请求');
		}
	}

	/**
	 * 寻找加密数据
	 * @return array|null 找到的加密数据或null
	 */
	private function findEncryptedData()
	{
		// 按优先级检查各种参数来源
		$sources = [
			'param' => input('param.'),
			'post' => input('post.'),
			'get' => input('get.'),
		];
		
		foreach ($sources as $sourceType => $data) {
			if ($this->isEncryptedRequest($data)) {
				\think\facade\Log::info("在{$sourceType}参数中发现加密数据");
				return $data;
			}
		}
		
		// 检查其他HTTP方法的请求体数据
		$requestMethod = request()->method();
		switch (strtoupper($requestMethod)) {
			case 'PUT':
				$putData = $this->getPutRequestData();
				if ($this->isEncryptedRequest($putData)) {
					\think\facade\Log::info('在PUT请求体中发现加密数据');
					return $putData;
				}
				break;
			case 'DELETE':
				$deleteData = $this->getDeleteRequestData();
				if ($this->isEncryptedRequest($deleteData)) {
					\think\facade\Log::info('在DELETE请求体中发现加密数据');
					return $deleteData;
				}
				break;
			case 'PATCH':
				$patchData = $this->getPatchRequestData();
				if ($this->isEncryptedRequest($patchData)) {
					\think\facade\Log::info('在PATCH请求体中发现加密数据');
					return $patchData;
				}
				break;
		}
		
		return null;
	}

	/**
	 * 将解密后的数据分发到各个参数容器
	 * @param array $decryptedData 解密后的数据
	 * @param string $requestMethod 请求方法
	 */
	private function distributeDecryptedData($decryptedData, $requestMethod)
	{
		// 首先移除原始的加密字段
		$encryptedFields = ['encrypted', 'key', 'data', 'iv', 'salt_signature'];
		foreach ($encryptedFields as $field) {
			unset($_REQUEST[$field]);
			if (strtoupper($requestMethod) === 'POST') {
				unset($_POST[$field]);
			} elseif (strtoupper($requestMethod) === 'GET') {
				unset($_GET[$field]);
			}
		}
		
		\think\facade\Log::info('🔧 已移除原始加密字段: ' . implode(', ', $encryptedFields));
		
		// 设置解密后的业务数据到全局变量，让input()函数能够获取
		// 优先使用payload数据，如果没有就使用整个解密结果，但要过滤掉加密相关字段
		if (isset($decryptedData['payload']) && is_array($decryptedData['payload'])) {
			$businessData = $decryptedData['payload'];
		} else {
			// 过滤掉加密相关字段
			$businessData = [];
			foreach ($decryptedData as $key => $value) {
				if (!in_array($key, ['encrypted', 'key', 'data', 'iv', 'salt', 'salt_signature', '_crypto_decrypted', '_crypto_salt', '_crypto_processed'])) {
					$businessData[$key] = $value;
				}
			}
		}
		
		// 调试：打印解密后的数据结构
		\think\facade\Log::info('🔍 解密后完整数据: ' . json_encode($decryptedData, JSON_UNESCAPED_UNICODE));
		\think\facade\Log::info('🔍 业务数据: ' . json_encode($businessData, JSON_UNESCAPED_UNICODE));
		
		// 确保$businessData是数组
		if (is_array($businessData)) {
			// 获取当前Request对象
			$request = request();
			
			// 获取现有参数（原始数据，不经过过滤）
			$currentParams = $request->param(false) ?: [];
			
			// 合并解密后的业务数据
			foreach ($businessData as $key => $value) {
				$currentParams[$key] = $value;
				
				// 同时设置到全局变量
				$_REQUEST[$key] = $value;
				if (strtoupper($requestMethod) === 'POST') {
					$_POST[$key] = $value;
				} elseif (strtoupper($requestMethod) === 'GET') {
					$_GET[$key] = $value;
				}
				
				\think\facade\Log::info("🔍 设置参数: {$key} = {$value}");
			}
			
			// 使用反射重新设置Request对象的param属性
			$reflection = new \ReflectionClass($request);
			if ($reflection->hasProperty('param')) {
				$paramProperty = $reflection->getProperty('param');
				$paramProperty->setAccessible(true);
				$paramProperty->setValue($request, $currentParams);
				\think\facade\Log::info("✅ 成功更新Request对象参数");
			}
		}
		
		// 标记已处理，避免重复解密
		$_REQUEST['_crypto_processed'] = true;
		if (strtoupper($requestMethod) === 'POST') {
			$_POST['_crypto_processed'] = true;
		} elseif (strtoupper($requestMethod) === 'GET') {
			$_GET['_crypto_processed'] = true;
		}
		
		\think\facade\Log::info('🔧 解密数据分发完成，当前$_REQUEST keys: ' . implode(', ', array_keys($_REQUEST)));
	}



	/**
	 * 获取PUT请求的数据
	 */
	private function getPutRequestData()
	{
		$input = file_get_contents('php://input');
		if (empty($input)) {
			return [];
		}

		$contentType = request()->header('content-type');
		if (strpos($contentType, 'application/json') !== false) {
			$data = json_decode($input, true);
			return is_array($data) ? $data : [];
		} elseif (strpos($contentType, 'application/x-www-form-urlencoded') !== false) {
			parse_str($input, $data);
			return is_array($data) ? $data : [];
		}

		return [];
	}

	/**
	 * 设置PUT请求的数据
	 */
	private function setPutRequestData($data)
	{
		foreach ($data as $key => $value) {
			if (!in_array($key, ['encrypted', 'key', 'data', 'iv', 'salt_signature', '_crypto_decrypted', '_crypto_salt'])) {
				$_REQUEST[$key] = $value;
				// 同时设置到PUT参数中（如果ThinkPHP支持）
				if (isset($_PUT)) {
					$_PUT[$key] = $value;
				}
			}
		}
	}

	/**
	 * 获取DELETE请求的数据
	 */
	private function getDeleteRequestData()
	{
		$input = file_get_contents('php://input');
		if (empty($input)) {
			return [];
		}

		$contentType = request()->header('content-type');
		if (strpos($contentType, 'application/json') !== false) {
			$data = json_decode($input, true);
			return is_array($data) ? $data : [];
		} elseif (strpos($contentType, 'application/x-www-form-urlencoded') !== false) {
			parse_str($input, $data);
			return is_array($data) ? $data : [];
		}

		return [];
	}

	/**
	 * 设置DELETE请求的数据
	 */
	private function setDeleteRequestData($data)
	{
		foreach ($data as $key => $value) {
			if (!in_array($key, ['encrypted', 'key', 'data', 'iv', 'salt_signature', '_crypto_decrypted', '_crypto_salt'])) {
				$_REQUEST[$key] = $value;
			}
		}
	}

	/**
	 * 获取PATCH请求的数据
	 */
	private function getPatchRequestData()
	{
		$input = file_get_contents('php://input');
		if (empty($input)) {
			return [];
		}

		$contentType = request()->header('content-type');
		if (strpos($contentType, 'application/json') !== false) {
			$data = json_decode($input, true);
			return is_array($data) ? $data : [];
		} elseif (strpos($contentType, 'application/x-www-form-urlencoded') !== false) {
			parse_str($input, $data);
			return is_array($data) ? $data : [];
		}

		return [];
	}

	/**
	 * 设置PATCH请求的数据
	 */
	private function setPatchRequestData($data)
	{
		foreach ($data as $key => $value) {
			if (!in_array($key, ['encrypted', 'key', 'data', 'iv', 'salt_signature', '_crypto_decrypted', '_crypto_salt'])) {
				$_REQUEST[$key] = $value;
			}
		}
	}

	/**
	 * 重写input函数行为，让它能获取到解密后的参数
	 */
	private function overrideInputBehavior()
	{
		// 如果已经处理过加密数据，则重新设置Request的内部数据
		if (isset($_REQUEST['_crypto_processed'])) {
			$request = request();
			
			// 不清空缓存，而是重新设置为正确的数据
			$reflection = new \ReflectionClass($request);
			
			// 重新设置param属性为当前的$_REQUEST数据
			if ($reflection->hasProperty('param')) {
				$paramProperty = $reflection->getProperty('param');
				$paramProperty->setAccessible(true);
				$paramProperty->setValue($request, $_REQUEST);
			}
			
			// 重新设置post属性为当前的$_POST数据
			if ($reflection->hasProperty('post')) {
				$postProperty = $reflection->getProperty('post');
				$postProperty->setAccessible(true);
				$postProperty->setValue($request, $_POST);
			}
			
			// 重新设置get属性为当前的$_GET数据
			if ($reflection->hasProperty('get')) {
				$getProperty = $reflection->getProperty('get');
				$getProperty->setAccessible(true);
				$getProperty->setValue($request, $_GET);
			}
			
			\think\facade\Log::info("✅ 已重新设置Request对象数据");
		}
	}

}
