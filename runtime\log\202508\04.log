---------------------------------------------------------------

[2025-08-04T16:00:49+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001169s ] mysql:host=mysql;dbname=di<PERSON><PERSON><PERSON>;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000594s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754294448 LIMIT 100 [ RunTime:0.000251s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000814s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000246s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754150400 LIMIT 600 [ RunTime:0.000229s ]
---------------------------------------------------------------

[2025-08-04T16:01:49+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000806s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000638s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754294509 LIMIT 100 [ RunTime:0.000226s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001187s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000506s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754150400 LIMIT 600 [ RunTime:0.000392s ]
---------------------------------------------------------------

[2025-08-04T16:02:50+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001330s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000567s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754294570 LIMIT 100 [ RunTime:0.000236s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001007s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000240s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754150400 LIMIT 600 [ RunTime:0.000353s ]
---------------------------------------------------------------

[2025-08-04T16:03:18+08:00] ********** POST localhost/api/Common/GetLanguage
[ error ] [0]语法解析错误: syntax error, unexpected 'protected' (T_PROTECTED), expecting end of file
---------------------------------------------------------------

[2025-08-04T16:03:30+08:00] ********** POST localhost/api/Common/GetLanguage
[ error ] [0]语法解析错误: syntax error, unexpected 'protected' (T_PROTECTED), expecting end of file
---------------------------------------------------------------

[2025-08-04T16:03:30+08:00] ********** POST localhost/api/task/getTaskList
[ error ] [0]语法解析错误: syntax error, unexpected 'protected' (T_PROTECTED), expecting end of file
---------------------------------------------------------------

[2025-08-04T16:03:45+08:00] ********** POST localhost/api/Common/BackData
[ error ] [0]语法解析错误: syntax error, unexpected 'protected' (T_PROTECTED), expecting end of file
---------------------------------------------------------------

[2025-08-04T16:03:51+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001096s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000465s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754294631 LIMIT 100 [ RunTime:0.000200s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000893s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000261s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754150400 LIMIT 600 [ RunTime:0.000484s ]
---------------------------------------------------------------

[2025-08-04T16:03:53+08:00] ********** GET localhost/
[ sql ] [ DB ] CONNECT:[ UseTime:0.001497s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001227s ]
[ sql ] [ SQL ] SELECT * FROM `ly_setting` LIMIT 1 [ RunTime:0.000719s ]
---------------------------------------------------------------

[2025-08-04T16:03:53+08:00] ********** POST localhost/api/Common/GetLanguage
[ error ] [0]语法解析错误: syntax error, unexpected 'protected' (T_PROTECTED), expecting end of file
---------------------------------------------------------------

[2025-08-04T16:03:53+08:00] ********** POST localhost/api/user/getUserInfo
[ error ] [0]语法解析错误: syntax error, unexpected 'protected' (T_PROTECTED), expecting end of file
---------------------------------------------------------------

[2025-08-04T16:03:53+08:00] ********** POST localhost/api/Common/BackData
[ error ] [0]语法解析错误: syntax error, unexpected 'protected' (T_PROTECTED), expecting end of file
---------------------------------------------------------------

[2025-08-04T16:03:53+08:00] ********** POST localhost/api/user/getUserInfo
[ error ] [0]语法解析错误: syntax error, unexpected 'protected' (T_PROTECTED), expecting end of file
---------------------------------------------------------------

[2025-08-04T16:03:53+08:00] ********** POST localhost/api/task/getTaskList
[ error ] [0]语法解析错误: syntax error, unexpected 'protected' (T_PROTECTED), expecting end of file
---------------------------------------------------------------

[2025-08-04T16:04:00+08:00] ********** POST localhost/api/Common/GetLanguage
[ error ] [0]语法解析错误: syntax error, unexpected 'protected' (T_PROTECTED), expecting end of file
---------------------------------------------------------------

[2025-08-04T16:04:00+08:00] ********** POST localhost/api/task/getTaskList
[ error ] [0]语法解析错误: syntax error, unexpected 'protected' (T_PROTECTED), expecting end of file
---------------------------------------------------------------

[2025-08-04T16:04:00+08:00] ********** POST localhost/api/Common/BackData
[ error ] [0]语法解析错误: syntax error, unexpected 'protected' (T_PROTECTED), expecting end of file
---------------------------------------------------------------

[2025-08-04T16:04:03+08:00] ********** POST localhost/api/Common/GetLanguage
[ error ] [0]语法解析错误: syntax error, unexpected 'protected' (T_PROTECTED), expecting end of file
---------------------------------------------------------------

[2025-08-04T16:04:03+08:00] ********** POST localhost/api/Common/BackData
[ error ] [0]语法解析错误: syntax error, unexpected 'protected' (T_PROTECTED), expecting end of file
---------------------------------------------------------------

[2025-08-04T16:04:03+08:00] ********** POST localhost/api/task/getTaskList
[ error ] [0]语法解析错误: syntax error, unexpected 'protected' (T_PROTECTED), expecting end of file
